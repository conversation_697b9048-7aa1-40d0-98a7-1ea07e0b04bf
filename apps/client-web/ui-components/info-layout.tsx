import { Container } from "@/ui-components/layout/container";
import { Heading, Text } from "@radix-ui/themes";
import classNames from "classnames";
import Image from "next/image";

export const InfoLayout = ({
  icon,
  iconAlt,
  title,
  description,
  children,
  className,
  smLeftAlign,
}: {
  icon: string;
  iconAlt: string;
  title: string;
  description: string;
  children?: React.ReactNode;
  className?: string;
  smLeftAlign?: boolean;
}) => {
  return (
    <Container className={className}>
      <div
        className={classNames(
          "col-span-full md:col-start-3 md:col-span-8 lg:col-start-4 lg:col-span-6 flex flex-col gap-6",
        )}
      >
        <Image
          className={smLeftAlign ? "md:mx-auto" : "mx-auto"}
          src={icon}
          alt={iconAlt}
          width={120}
          height={120}
        />
        <div
          className={classNames(
            "flex flex-col",
            smLeftAlign ? "md:text-center" : "text-center",
          )}
        >
          <div className="flex flex-col gap-4">
            <Heading as="h2" size="7" weight="medium" className="mb-4">
              {title}
            </Heading>

            <Text size="2" color="gray">
              {description}
            </Text>
          </div>
        </div>

        {children}
      </div>
    </Container>
  );
};
