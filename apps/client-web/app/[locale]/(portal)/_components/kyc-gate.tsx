"use client";
import { useAuth } from "@/hooks/use-auth";
import { redirect } from "next/navigation";
import { PropsWithChildren } from "react";

export type GateType = "component" | "route";

export const KycGate = ({
  children,
  type,
}: PropsWithChildren<{ type: GateType }>) => {
  const { userData } = useAuth();

  const { userStatus } = userData;

  if (userStatus === undefined) {
    return null;
  }

  if (
    userStatus === "ACTIVE" ||
    userStatus === "PERMANENTLY_RESTRICTED" ||
    userStatus === "SUSPENDED"
  ) {
    return children;
  }

  if (type === "component") {
    return null;
  }
  if (type === "route") {
    return redirect("/kyc");
  }
};
