import { useTranslations } from "next-intl";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { CopyButton } from "@/ui-components/copy-button";
import { InvestmentPayoutDTO } from "@/api/data-contracts";

import { AmountDisplay } from "@repo/ui/amount-display";
import { formatDate } from "../../../transaction-history/utils";

export const usePayoutColumns = () => {
  const t = useTranslations("Portal.PayoutHistory");
  const columnHelper = createColumnHelper<InvestmentPayoutDTO>();

  const columns: ColumnDef<InvestmentPayoutDTO, any>[] = [
    columnHelper.accessor("id", {
      minSize: 320,
      size: 320,
      header: t("transactionId"),
      cell: (info) => (
        <div className="flex items-center gap-2">
          <span>{info.getValue()}</span>
          <CopyButton text={info.getValue() || ""} />
        </div>
      ),
    }),
    columnHelper.accessor("amount", {
      size: 200,
      header: t("investedAmount"),
      cell: (info) => {
        const amount = info.getValue();
        return <AmountDisplay amount={amount} />;
      },
    }),
    columnHelper.accessor("payoutDate", {
      size: 200,
      header: t("nextPayoutDate"),
      cell: (info) => {
        const value = info.getValue();
        return <span className="text-nowrap">{formatDate(value)}</span>;
      },
    }),
    columnHelper.accessor("currency", {
      header: t("currency"),
    }),
  ];

  return columns;
};
