import { Table } from "@radix-ui/themes";
import { InvestmentPayoutDTO } from "@/api/data-contracts";
import { InfoLayout } from "@repo/ui/info-layout";
import { getCommonPinningStyles } from "@repo/ui/utils/table-utils";

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import style from "../../../transaction-history/_components/index.module.scss";

import { LoadingPlaceholder } from "@repo/ui/loading-placeholder";
import { usePayoutColumns } from "./use-payout-columns";

export const PayoutTable = ({
  data,
  isLoading = false,
}: {
  data: InvestmentPayoutDTO[];
  isLoading?: boolean;
}) => {
  const columns = usePayoutColumns();

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (!data.length) {
    if (isLoading) {
      return <LoadingPlaceholder />;
    }
    return (
      <div className="bg-[#00000008] rounded-lg">
        <InfoLayout
          className="py-10"
          icon="/graphics/orange/empty-file.png"
          iconAlt="no data"
          title={"No asset holdings"}
          description={
            "All approved transactions along with their respective payout dates will be displayed here."
          }
        />
      </div>
    );
  }

  return (
    <Table.Root
      variant="surface"
      size="3"
      layout="fixed"
      className={style.override}
    >
      <Table.Header>
        {table.getHeaderGroups().map((headerGroup) => (
          <Table.Row align="center" key={headerGroup.id}>
            {headerGroup.headers.map((header) => (
              <Table.ColumnHeaderCell
                key={header.id}
                style={{ ...getCommonPinningStyles(header.column, true) }}
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </Table.ColumnHeaderCell>
            ))}
          </Table.Row>
        ))}
      </Table.Header>
      <Table.Body>
        {table.getRowModel().rows.map((row) => (
          <Table.Row align="center" key={row.id}>
            {row.getVisibleCells().map((cell) => (
              <Table.Cell
                key={cell.id}
                style={{ ...getCommonPinningStyles(cell.column) }}
              >
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </Table.Cell>
            ))}
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  );
};
