import { Link } from "@/i18n/navigation";
import { PrimaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@repo/ui/info-layout";
import { useTranslations } from "next-intl";

export const VerifyingIdentity = () => {
  const t = useTranslations("Portal.KYC.verifyingIdentity");

  return (
    <InfoLayout
      className="py-20 md:py-10 xl:py-20"
      icon="/graphics/orange/verified.png"
      iconAlt="verified"
      title={t("verifiedTitle")}
      description={t("verifiedDescription")}
    >
      <div className="flex justify-center">
        <PrimaryButton asChild>
          <Link href={"/dashboard"}>{t("continueToDashboard")}</Link>
        </PrimaryButton>
      </div>
    </InfoLayout>
  );
};
