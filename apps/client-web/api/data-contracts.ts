/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface ResponseResultObject {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: any;
  requestId?: string;
}

export interface ResponseResultVoid {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: any;
  requestId?: string;
}

export interface KycOnboardingMultipartFormData {
  /** KYC metadata as JSON. This part must be application/json. */
  data?: KycVerificationRequestData;
  /**
   * Front of document (e.g. ID card/passport). This part is an octet-stream.
   * @format binary
   */
  documentFront?: File;
  /**
   * Back of document (optional). This part is an octet-stream.
   * @format binary
   */
  documentBack?: File;
  /**
   * Selfie photo. This part is an octet-stream.
   * @format binary
   */
  selfie?: File;
}

export interface KycVerificationRequestData {
  /** @minLength 1 */
  firstName: string;
  /** @minLength 1 */
  lastName: string;
  /** @minLength 1 */
  nationality: string;
  /** @minLength 1 */
  countryOfResidence: string;
  identityDocumentType: "NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT";
}

export interface SubmitFiatDepositMultipartFormData {
  /** FIAT deposit metadata as JSON. This part must be application/json. */
  data?: SubmitFiatDepositRequestData;
  /**
   * Proof of transfer document. This part is an octet-stream.
   * @format binary
   */
  proofOfTransfer?: File;
}

export interface SubmitFiatDepositRequestData {
  /** @max ************ */
  amount: number;
  /**
   * @minLength 1
   * @maxLength 255
   */
  bankName: string;
  /**
   * @minLength 1
   * @pattern ^\d+$
   */
  accountNumber: string;
}

export interface SubmitCryptoDepositRequest {
  /**
   * @min 0.01
   * @max ************
   */
  amount: number;
  /** @minLength 1 */
  userWalletAddress: string;
  /** @minLength 1 */
  transactionHash: string;
}

export interface ResponseResultUserProfileDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: UserProfileDTO;
  requestId?: string;
}

export interface UserProfileDTO {
  publicId?: string;
  email?: string;
  /**
   * User status, not directly linked to KYC level
   * @default "0"
   */
  userStatus?:
    | "PENDING_KYC"
    | "ACTIVE"
    | "SUSPENDED"
    | "PERMANENTLY_RESTRICTED";
  /** @default "-" */
  firstName?: string;
  lastName?: string;
}

export interface PagedTransactionDTO {
  records?: TransactionDTO[];
  /** @format int32 */
  totalItems?: number;
  /**
   * 1-indexed page number
   * @format int32
   * @min 1
   */
  page?: number;
  /**
   * @format int32
   * @min 1
   * @max 100
   * @default 20
   */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
}

export interface ResponseResultPagedTransactionDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: PagedTransactionDTO;
  requestId?: string;
}

export interface TransactionDTO {
  /** @format uuid */
  id?: string;
  /** @format int64 */
  userId?: number;
  /** @format int64 */
  accountId?: number;
  transactionType?: "INVEST_FIAT_DEPOSIT" | "INVEST_CRYPTO_DEPOSIT";
  status?: "PENDING" | "FAILED" | "SUCCESSFUL";
  /** @format double */
  amount?: number;
  currency?: string;
  description?: string;
  /** @format uuid */
  referenceId?: string;
  /** @format date-time */
  createdAt?: string;
  cryptoTransaction?: boolean;
}

/** Details for a Crypto deposit transaction. */
export type CryptoDepositDetails = TransactionTypeSpecificDetails & {
  /** Deposit type, always CRYPTO for this object. */
  depositType?: "FIAT" | "CRYPTO";
  /** The crypto network used for the deposit. */
  network?: "TRON";
  /** The crypto currency of the deposit. */
  currency?: "USDT";
  /** Sender's wallet address. */
  walletAddress?: string;
  /** Transaction hash (or ID) on the blockchain. */
  transactionHash?: string;
  /** Recipient's wallet address. */
  recipientAddress?: string;
  /** Reason for rejection, if the deposit was rejected. */
  rejectionReason?: string;
};

/** Details for a FIAT deposit transaction. */
export type FiatDepositDetails = TransactionTypeSpecificDetails & {
  /** Deposit type, always FIAT for this object. */
  depositType?: "FIAT" | "CRYPTO";
  /** Name of the sender's bank. */
  bankName?: string;
  /** Sender's bank account number. */
  accountNumber?: string;
  /** Name on the sender's bank account. */
  accountName?: string;
  /** Recipient's bank account number. */
  recipientAccountNumber?: string;
  /** Reason for rejection, if the deposit was rejected. */
  rejectionReason?: string;
};

/** Represents a monetary value with its amount and currency. */
export interface MoneyDTO {
  /**
   * The numerical amount of money.
   * @example 10000
   */
  amount?: number;
  /**
   * The three-letter ISO currency code.
   * @example "USD"
   */
  currency?: string;
}

export interface ResponseResultTransactionDetailsDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  /** Represents the detailed information of a transaction. */
  data?: TransactionDetailsDTO;
  requestId?: string;
}

/** Represents the detailed information of a transaction. */
export interface TransactionDetailsDTO {
  /**
   * Transaction ID
   * @format uuid
   */
  id?: string;
  /** Amount and currency of the transaction */
  amount?: MoneyDTO;
  /** Current status of the transaction */
  status?: "PENDING" | "FAILED" | "SUCCESSFUL";
  /** Type of the transaction */
  type?: "INVEST_FIAT_DEPOSIT" | "INVEST_CRYPTO_DEPOSIT";
  /**
   * Timestamp of when the transaction was created
   * @format date-time
   */
  createdAt?: string;
  /** Details specific to the transaction type */
  details?: CryptoDepositDetails | FiatDepositDetails;
}

export interface TransactionTypeSpecificDetails {
  depositType: string;
}

export interface ResponseResultKycStatus {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED";
  requestId?: string;
}

export interface IdentityDocumentTypeDTO {
  name?: "NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT";
}

export interface ResponseResultListIdentityDocumentTypeDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: IdentityDocumentTypeDTO[];
  requestId?: string;
}

export interface CountryDTO {
  alpha2?: string;
  name?: string;
}

export interface ResponseResultListCountryDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: CountryDTO[];
  requestId?: string;
}

export interface AccountSummaryDTO {
  totalBalance?: number;
  currency?: string;
  assetBreakdown?: AssetBreakdownDTO;
}

export interface AssetBreakdownDTO {
  crypto?: DepositSummaryDTO[];
  fiat?: DepositSummaryDTO[];
}

export interface DashboardResponse {
  userInfo?: UserInfoDTO;
  /** Asset information */
  accountSummary?: AccountSummaryDTO;
  recentTransactions?: TransactionSummaryDTO[];
}

export interface DepositSummaryDTO {
  amount?: number;
  currency?: string;
}

export interface ResponseResultDashboardResponse {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: DashboardResponse;
  requestId?: string;
}

export interface TransactionSummaryDTO {
  id?: string;
  /**
   * Transaction date in ISO 8601 format (YYYY-MM-DDTHH:mm:ss±HH:mm)
   * @example "2024-01-15T14:30:45.*********+08:00"
   */
  createdAt?: string;
  transactionType?: "INVEST_FIAT_DEPOSIT" | "INVEST_CRYPTO_DEPOSIT";
  currency?: string;
  /** @format double */
  amount?: number;
  status?: "PENDING" | "FAILED" | "SUCCESSFUL";
}

export interface UserInfoDTO {
  publicId?: string;
  /** @default "-" */
  firstName?: string;
  lastName?: string;
  /**
   * User status, not directly linked to KYC level
   * @default "0"
   */
  status?: "PENDING_KYC" | "ACTIVE" | "SUSPENDED" | "PERMANENTLY_RESTRICTED";
}

export interface InvestmentPayoutDTO {
  id?: string;
  currency?: string;
  /** @format double */
  amount?: number;
  /**
   * Date in ISO 8601 format (YYYY-MM-DDTHH:mm:ss±HH:mm)
   * @example "2024-01-15T14:30:45.*********+08:00"
   */
  payoutDate?: string;
}

export interface PagedInvestmentPayoutDTO {
  records?: InvestmentPayoutDTO[];
  /** @format int32 */
  totalItems?: number;
  /**
   * 1-indexed page number
   * @format int32
   * @min 1
   */
  page?: number;
  /**
   * @format int32
   * @min 1
   * @max 100
   * @default 20
   */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
}

export interface ResponseResultPagedInvestmentPayoutDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: PagedInvestmentPayoutDTO;
  requestId?: string;
}
