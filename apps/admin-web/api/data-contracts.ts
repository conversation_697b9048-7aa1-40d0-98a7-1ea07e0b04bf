/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface ResponseResultObject {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: any;
  requestId?: string;
}

export interface ResponseResultVoid {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: any;
  requestId?: string;
}

export interface KycReviewRequest {
  /** @format int64 */
  submissionId: number;
  action: "APPROVE" | "REJECT" | "RESTRICT";
  reason?:
    | "APPROVED"
    | "INVALID_DOCUMENT_TYPE"
    | "DOCUMENT_EXPIRED"
    | "DOCUMENT_UNREADABLE_BLURRY"
    | "DOCUMENT_DAMAGED_OR_TAMPERED"
    | "SELFIE_MISMATCH"
    | "INCONSISTENT_PERSONAL_DETAILS"
    | "SUSPICIOUS_ACTIVITY"
    | "SANCTIONS_LIST_MATCH"
    | "OTHER_REASON";
  /**
   * @minLength 0
   * @maxLength 500
   */
  reasonExtra?: string;
}

/** Represents a monetary value with its amount and currency. */
export interface MoneyDTO {
  /**
   * The numerical amount of money.
   * @example 10000
   */
  amount?: number;
  /**
   * The three-letter ISO currency code.
   * @example "USD"
   */
  currency?: string;
}

export interface PagedUserSummaryResponse {
  records?: UserSummaryResponse[];
  /** @format int32 */
  totalItems?: number;
  /**
   * 1-indexed page number
   * @format int32
   * @min 1
   */
  page?: number;
  /**
   * @format int32
   * @min 1
   * @max 100
   * @default 20
   */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
}

export interface ResponseResultPagedUserSummaryResponse {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: PagedUserSummaryResponse;
  requestId?: string;
}

export interface UserSummaryResponse {
  publicId?: string;
  email?: string;
  /** @format date-time */
  createdAt?: string;
  /** Represents a monetary value with its amount and currency. */
  totalInvestment?: MoneyDTO;
  countryOfResidence?: string;
}

export interface OperatorDTO {
  /** @format int64 */
  id?: number;
  email?: string;
  firstName?: string;
  lastName?: string;
}

export interface ResponseResultOperatorDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: OperatorDTO;
  requestId?: string;
}

/** KYC verification submission details */
export interface KycVerificationSearchResponse {
  publicId?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  nationality?: string;
  countryOfResidence?: string;
  identityDocumentType?: "NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT";
  documentFrontKey?: string;
  documentBackKey?: string;
  selfieKey?: string;
  /** @format int64 */
  submissionId?: number;
  status?: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED";
  /** @format date-time */
  submissionDate?: string;
  /** @format date-time */
  lastUpdated?: string;
  operatorName?: string;
  actionReason?:
    | "APPROVED"
    | "INVALID_DOCUMENT_TYPE"
    | "DOCUMENT_EXPIRED"
    | "DOCUMENT_UNREADABLE_BLURRY"
    | "DOCUMENT_DAMAGED_OR_TAMPERED"
    | "SELFIE_MISMATCH"
    | "INCONSISTENT_PERSONAL_DETAILS"
    | "SUSPICIOUS_ACTIVITY"
    | "SANCTIONS_LIST_MATCH"
    | "OTHER_REASON";
}

export interface PagedKycVerificationSearchResponse {
  records?: KycVerificationSearchResponse[];
  /** @format int32 */
  totalItems?: number;
  /**
   * 1-indexed page number
   * @format int32
   * @min 1
   */
  page?: number;
  /**
   * @format int32
   * @min 1
   * @max 100
   * @default 20
   */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
}

export interface ResponseResultPagedKycVerificationSearchResponse {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: PagedKycVerificationSearchResponse;
  requestId?: string;
}

export interface KycOperatorReasonDTO {
  reason?:
    | "APPROVED"
    | "INVALID_DOCUMENT_TYPE"
    | "DOCUMENT_EXPIRED"
    | "DOCUMENT_UNREADABLE_BLURRY"
    | "DOCUMENT_DAMAGED_OR_TAMPERED"
    | "SELFIE_MISMATCH"
    | "INCONSISTENT_PERSONAL_DETAILS"
    | "SUSPICIOUS_ACTIVITY"
    | "SANCTIONS_LIST_MATCH"
    | "OTHER_REASON";
  /** Localized text */
  text?: string;
}

export interface ResponseResultListKycOperatorReasonDTO {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: KycOperatorReasonDTO[];
  requestId?: string;
}

export interface PresignedURLResponse {
  /** @format url */
  url?: string;
  /** @format date-time */
  expiration?: string;
}

export interface ResponseResultPresignedURLResponse {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: PresignedURLResponse;
  requestId?: string;
}

export interface AllocationDTO {
  currency?: string;
  percentage?: number;
}

export interface AssetBreakdownDTO {
  asset?: string;
  /** Represents a monetary value with its amount and currency. */
  totalAmount?: MoneyDTO;
  allocation?: number;
}

export interface PortfolioOverviewResponse {
  totalInvestments?: TotalInvestmentDTO;
  allocation?: AllocationDTO[];
  breakdown?: AssetBreakdownDTO[];
}

export interface ResponseResultPortfolioOverviewResponse {
  /**
   * Business Code
   * @default "0"
   */
  code?: string;
  message?: string;
  data?: PortfolioOverviewResponse;
  requestId?: string;
}

export interface TotalInvestmentDTO {
  /** Represents a monetary value with its amount and currency. */
  totalAmountUSD?: MoneyDTO;
}
